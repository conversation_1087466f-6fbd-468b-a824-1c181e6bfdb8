This XML file does not appear to have any style information associated with it. The document tree is shown below.
<flow-definition plugin="workflow-job@1400.v7fd111b_ec82f">
<actions>
<org.jenkinsci.plugins.pipeline.modeldefinition.actions.DeclarativeJobAction plugin="pipeline-model-definition@2.2198.v41dd8ef6dd56"/>
<org.jenkinsci.plugins.workflow.multibranch.JobPropertyTrackerAction plugin="workflow-multibranch@773.vc4fe1378f1d5">
<jobPropertyDescriptors>
<string>hudson.model.ParametersDefinitionProperty</string>
</jobPropertyDescriptors>
</org.jenkinsci.plugins.workflow.multibranch.JobPropertyTrackerAction>
<org.jenkinsci.plugins.pipeline.modeldefinition.actions.DeclarativeJobPropertyTrackerAction plugin="pipeline-model-definition@2.2198.v41dd8ef6dd56">
<jobProperties>
<string>jenkins.model.BuildDiscarderProperty</string>
</jobProperties>
<triggers/>
<parameters>
</parameters>
<options/>
</org.jenkinsci.plugins.pipeline.modeldefinition.actions.DeclarativeJobPropertyTrackerAction>
</actions>
<description/>
<keepDependencies>false</keepDependencies>
<properties>
<hudson.plugins.jira.JiraProjectProperty plugin="jira@3.13"/>
<hudson.plugins.buildblocker.BuildBlockerProperty plugin="build-blocker-plugin@165.v5ecb_fb_f61520">
<useBuildBlocker>false</useBuildBlocker>
<blockLevel>GLOBAL</blockLevel>
<scanQueueFor>DISABLED</scanQueueFor>
<blockingJobs/>
</hudson.plugins.buildblocker.BuildBlockerProperty>
<jenkins.model.BuildDiscarderProperty>
<strategy class="hudson.tasks.LogRotator">
<daysToKeep>-1</daysToKeep>
<numToKeep>20</numToKeep>
<artifactDaysToKeep>10</artifactDaysToKeep>
<artifactNumToKeep>10</artifactNumToKeep>
</strategy>
</jenkins.model.BuildDiscarderProperty>
<com.dabsquared.gitlabjenkins.connection.GitLabConnectionProperty plugin="gitlab-plugin@1.8.0">
<gitLabConnection>gitdop</gitLabConnection>
<jobCredentialId/>
<useAlternativeCredential>false</useAlternativeCredential>
</com.dabsquared.gitlabjenkins.connection.GitLabConnectionProperty>
<com.synopsys.arc.jenkinsci.plugins.jobrestrictions.jobs.JobRestrictionProperty plugin="job-restrictions@0.8"/>
<hudson.model.ParametersDefinitionProperty>
<parameterDefinitions>
<hudson.model.StringParameterDefinition>
<name>CHANGE_NO</name>
<description>Please enter Change Request Number value here</description>
<trim>false</trim>
</hudson.model.StringParameterDefinition>
<hudson.model.StringParameterDefinition>
<name>GIT_COMMIT_ID</name>
<description>Please insert git commit id</description>
<trim>false</trim>
</hudson.model.StringParameterDefinition>
<net.uaznia.lukanus.hudson.plugins.gitparameter.GitParameterDefinition plugin="git-parameter@0.9.19">
<name>VERSION</name>
<description>Please choose Jenkinsfile version (git tag) to run</description>
<uuid>4ca9f9e9-c147-4358-8abb-775da121e926</uuid>
<type>PT_TAG</type>
<branch/>
<tagFilter>[0-9]*</tagFilter>
<branchFilter>.*</branchFilter>
<sortMode>DESCENDING_SMART</sortMode>
<defaultValue/>
<selectedValue>NONE</selectedValue>
<quickFilterEnabled>true</quickFilterEnabled>
<listSize>1</listSize>
<requiredParameter>false</requiredParameter>
</net.uaznia.lukanus.hudson.plugins.gitparameter.GitParameterDefinition>
<net.uaznia.lukanus.hudson.plugins.gitparameter.GitParameterDefinition plugin="git-parameter@0.9.19">
<name>BASE_IMAGE_VERSION</name>
<description>
scbseaacr001nonprod.azurecr.io/ap2404-cbsdps/robot:${BASE_IMAGE_VERSION}
</description>
<uuid>2c2d4d8f-c120-406f-8670-9c31a273cdc9</uuid>
<type>PT_TAG</type>
<branch/>
<tagFilter>*</tagFilter>
<branchFilter>.*</branchFilter>
<sortMode>DESCENDING_SMART</sortMode>
<defaultValue/>
<selectedValue>NONE</selectedValue>
<quickFilterEnabled>true</quickFilterEnabled>
<listSize>1</listSize>
<requiredParameter>false</requiredParameter>
</net.uaznia.lukanus.hudson.plugins.gitparameter.GitParameterDefinition>
<hudson.model.StringParameterDefinition>
<name>ROBOT_CLI</name>
<description>Please insert robot cli here</description>
<defaultValue>
robot -v env:sit --pythonpath ./keywords/python_library --listener listener_excel.ExcelListener -d ./log/$(date +%Y%m%d%H%M) test_cases/integration.robot
</defaultValue>
<trim>false</trim>
</hudson.model.StringParameterDefinition>
<hudson.model.StringParameterDefinition>
<name>emailReceiver</name>
<defaultValue><EMAIL></defaultValue>
<trim>false</trim>
</hudson.model.StringParameterDefinition>
</parameterDefinitions>
</hudson.model.ParametersDefinitionProperty>
</properties>
<definition class="org.jenkinsci.plugins.workflow.cps.CpsScmFlowDefinition" plugin="workflow-cps@3894.vd0f0248b_a_fc4">
<scm class="hudson.plugins.git.GitSCM" plugin="git@5.2.1">
<configVersion>2</configVersion>
<userRemoteConfigs>
<hudson.plugins.git.UserRemoteConfig>
<url>
***********************:ap2404-cbsdps/qa/scb-qa-automation-deposit.git
</url>
<credentialsId>gitlab-deploy-key</credentialsId>
</hudson.plugins.git.UserRemoteConfig>
</userRemoteConfigs>
<branches>
<hudson.plugins.git.BranchSpec>
<name>${VERSION}</name>
</hudson.plugins.git.BranchSpec>
</branches>
<doGenerateSubmoduleConfigurations>false</doGenerateSubmoduleConfigurations>
<submoduleCfg class="empty-list"/>
<extensions/>
</scm>
<scriptPath>Jenkinsfile</scriptPath>
<lightweight>false</lightweight>
</definition>
<triggers/>
<disabled>false</disabled>
</flow-definition>